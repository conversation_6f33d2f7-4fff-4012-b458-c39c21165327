// Standalone U-shape cutter function
// Usage: uShapeCutter(imageSrc).then(dataUrl => ...)
function uShapeCutter(src) {
  return new Promise((resolve, reject) => {
    const img = new window.Image();
    img.crossOrigin = 'Anonymous';
    img.src = src;
    img.onload = () => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      canvas.width = img.naturalWidth;
      canvas.height = img.naturalHeight;
      ctx.drawImage(img, 0, 0);
      // U-shape params (from bracelet.html)
      const openingWidthPercent = 90;
      const cHeightPercent = 80;
      const thicknessPercent = 20;
      const verticalPosPercent = 50;
      const horizontalPosPercent = 23;
      const canvasWidth = canvas.width;
      const canvasHeight = canvas.height;
      const uWidth = (canvasWidth * openingWidthPercent) / 100;
      const uHeight = (canvasHeight * cHeightPercent) / 100;
      const uThickness = (Math.min(canvasWidth, canvasHeight) * thicknessPercent) / 100;
      const centerX = (canvasWidth * verticalPosPercent) / 100;
      const centerY = (canvasHeight * horizontalPosPercent) / 100;
      ctx.fillStyle = 'white';
      ctx.beginPath();
      const outerRadius = uThickness / 2;
      const outerLeft = centerX - uWidth / 2;
      const outerTop = centerY - uHeight / 2;
      const outerRight = centerX + uWidth / 2;
      const outerBottom = centerY + uHeight / 2;
      ctx.moveTo(outerLeft + outerRadius, outerBottom);
      ctx.lineTo(outerRight - outerRadius, outerBottom);
      ctx.quadraticCurveTo(outerRight, outerBottom, outerRight, outerBottom - outerRadius);
      ctx.lineTo(outerRight, outerTop + outerRadius);
      ctx.quadraticCurveTo(outerRight, outerTop, outerRight - outerRadius, outerTop);
      ctx.lineTo(centerX + uThickness/2 + outerRadius, outerTop);
      ctx.quadraticCurveTo(centerX + uThickness/2, outerTop, centerX + uThickness/2, outerTop + outerRadius);
      ctx.lineTo(centerX + uThickness/2, centerY - uThickness/2);
      ctx.lineTo(centerX - uThickness/2, centerY - uThickness/2);
      ctx.lineTo(centerX - uThickness/2, outerTop + outerRadius);
      ctx.quadraticCurveTo(centerX - uThickness/2, outerTop, centerX - uThickness/2 - outerRadius, outerTop);
      ctx.lineTo(outerLeft + outerRadius, outerTop);
      ctx.quadraticCurveTo(outerLeft, outerTop, outerLeft, outerTop + outerRadius);
      ctx.lineTo(outerLeft, outerBottom - outerRadius);
      ctx.quadraticCurveTo(outerLeft, outerBottom, outerLeft + outerRadius, outerBottom);
      ctx.closePath();
      ctx.fill();
      resolve(canvas.toDataURL('image/png'));
    };
    img.onerror = reject;
  });
}

export default uShapeCutter; 