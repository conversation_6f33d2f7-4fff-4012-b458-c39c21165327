/**
 * Utility for removing white backgrounds from images
 * Only removes pure white and very close whites to preserve light gray watches
 */

/**
 * Remove white background from an image
 * @param {string} imageSrc - Image source URL or data URL
 * @param {number} tolerance - Color tolerance for white detection (0-255, default: 5)
 * @returns {Promise<string>} - Data URL of processed image
 */
export const removeWhiteBackground = async (imageSrc, tolerance = 5) => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';

    img.onload = () => {
      try {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        canvas.width = img.width;
        canvas.height = img.height;

        // Draw the image
        ctx.drawImage(img, 0, 0);

        // Get image data
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;

        // Create a copy of the image data for edge detection
        const edgeData = new Uint8ClampedArray(data.length);
        edgeData.set(data);

        // Process pixels
        for (let i = 0; i < data.length; i += 4) {
          const red = data[i];
          const green = data[i + 1];
          const blue = data[i + 2];

          // Check if pixel is white or very close to white
          const isWhite = isWhitePixel(red, green, blue, tolerance);

          if (isWhite) {
            // Check if this is an edge pixel
            const isEdge = isEdgePixel(edgeData, i, canvas.width);
            
            if (!isEdge) {
              // Make pixel transparent only if it's not an edge
              data[i + 3] = 0;
            }
          }
        }

        // Put the modified image data back
        ctx.putImageData(imageData, 0, 0);

        // Convert to data URL
        const processedImageUrl = canvas.toDataURL('image/png');
        resolve(processedImageUrl);
      } catch (error) {
        reject(error);
      }
    };

    img.onerror = () => {
      reject(new Error('Failed to load image'));
    };

    img.src = imageSrc;
  });
};

/**
 * Check if a pixel is white or very close to white
 * @param {number} r - Red value (0-255)
 * @param {number} g - Green value (0-255)
 * @param {number} b - Blue value (0-255)
 * @param {number} tolerance - Tolerance for white detection
 * @returns {boolean} - True if pixel is considered white
 */
const isWhitePixel = (r, g, b, tolerance) => {
  // Pure white check
  if (r === 255 && g === 255 && b === 255) {
    return true;
  }

  // Near-white check with tolerance
  // Only consider pixels that are very close to pure white
  const whiteThreshold = 255 - tolerance;
  return r >= whiteThreshold && g >= whiteThreshold && b >= whiteThreshold;
};

/**
 * Check if a pixel is part of an edge
 * @param {Uint8ClampedArray} data - Image data
 * @param {number} index - Pixel index
 * @param {number} width - Image width
 * @returns {boolean} - True if pixel is part of an edge
 */
const isEdgePixel = (data, index, width) => {
  const pixelSize = 4;
  const height = data.length / (width * pixelSize);
  const x = (index / pixelSize) % width;
  const y = Math.floor((index / pixelSize) / width);

  // Skip edge pixels of the image
  if (x === 0 || x === width - 1 || y === 0 || y === height - 1) {
    return true;
  }

  // Check surrounding pixels for non-white colors
  const surroundingPixels = [
    data[index - width * pixelSize - pixelSize], // top-left
    data[index - width * pixelSize], // top
    data[index - width * pixelSize + pixelSize], // top-right
    data[index - pixelSize], // left
    data[index + pixelSize], // right
    data[index + width * pixelSize - pixelSize], // bottom-left
    data[index + width * pixelSize], // bottom
    data[index + width * pixelSize + pixelSize] // bottom-right
  ];

  // If any surrounding pixel is not white, this is an edge pixel
  return surroundingPixels.some((pixel, i) => {
    if (pixel === undefined) return false;
    const r = data[index - width * pixelSize - pixelSize + (i * pixelSize)];
    const g = data[index - width * pixelSize - pixelSize + (i * pixelSize) + 1];
    const b = data[index - width * pixelSize - pixelSize + (i * pixelSize) + 2];
    return !isWhitePixel(r, g, b, 5);
  });
};

/**
 * Batch process multiple images to remove white backgrounds
 * @param {string[]} imageSrcs - Array of image source URLs
 * @param {number} tolerance - Color tolerance for white detection
 * @returns {Promise<string[]>} - Array of processed image data URLs
 */
export const batchRemoveWhiteBackground = async (imageSrcs, tolerance = 10) => {
  const promises = imageSrcs.map(src => removeWhiteBackground(src, tolerance));
  return Promise.all(promises);
};

/**
 * Process an image file and return processed data URL
 * @param {File} file - Image file
 * @param {number} tolerance - Color tolerance for white detection
 * @returns {Promise<string>} - Processed image data URL
 */
export const processImageFile = async (file, tolerance = 10) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = async (e) => {
      try {
        const processedImage = await removeWhiteBackground(e.target.result, tolerance);
        resolve(processedImage);
      } catch (error) {
        reject(error);
      }
    };

    reader.onerror = () => {
      reject(new Error('Failed to read file'));
    };

    reader.readAsDataURL(file);
  });
};

/**
 * Check if an image has a white background
 * @param {string} imageSrc - Image source URL
 * @returns {Promise<boolean>} - True if image has significant white background
 */
export const hasWhiteBackground = async (imageSrc) => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';

    img.onload = () => {
      try {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        canvas.width = img.width;
        canvas.height = img.height;
        ctx.drawImage(img, 0, 0);

        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;

        let whitePixelCount = 0;
        const totalPixels = data.length / 4;

        for (let i = 0; i < data.length; i += 4) {
          const red = data[i];
          const green = data[i + 1];
          const blue = data[i + 2];

          if (isWhitePixel(red, green, blue, 10)) {
            whitePixelCount++;
          }
        }

        // Consider image to have white background if more than 30% pixels are white
        const whitePercentage = (whitePixelCount / totalPixels) * 100;
        resolve(whitePercentage > 30);
      } catch (error) {
        reject(error);
      }
    };

    img.onerror = () => {
      reject(new Error('Failed to load image'));
    };

    img.src = imageSrc;
  });
};
