import React, { useState, useEffect } from 'react';
import AdminSidebar from '../../components/admin/AdminSidebar';
import AdminNavbar from '../../components/admin/AdminNavbar';
import { motion } from 'framer-motion';
import { Search, Plus, Eye, Edit, Trash2, Globe, TrendingUp, Users, Code } from 'lucide-react';

function generatePassword() {
  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+';
  let password = '';
  for (let i = 0; i < 12; i++) {
    password += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return password;
}

const Clients = () => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [collapsed, setCollapsed] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [showModal, setShowModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showCodeModal, setShowCodeModal] = useState(false);
  const [selectedClient, setSelectedClient] = useState(null);
  const [editingClient, setEditingClient] = useState(null);
  const [clients, setClients] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [stats, setStats] = useState({
    newClientsThisMonth: 0,
    activeRate: 0,
    tryOnsGrowth: 0,
    revenueGrowth: 0,
    avgSessionsPerUser: 0
  });
  const [clientForm, setClientForm] = useState({
    companyName: '',
    contactName: '',
    website: '',
    email: '',
    password: '',
    phone: '',
    industry: '',
    productType: 'watches',
    subscriptionPlan: 'basic'
  });

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  // Calculate margin for main content
  const mainMargin = collapsed ? 'md:ml-[80px]' : 'md:ml-[280px]';

  // Fetch clients from backend
  useEffect(() => {
    fetchClients();
  }, [searchQuery, selectedStatus]);

  const fetchClients = async () => {
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';
      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;

      const params = new URLSearchParams();
      if (searchQuery) params.append('search', searchQuery);
      if (selectedStatus !== 'all') params.append('status', selectedStatus);

      const response = await fetch(`${apiUrl}/api/clients?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to fetch clients');
      }

      const data = await response.json();
      setClients(data.clients || []);
      setStats(data.stats || {
        newClientsThisMonth: 0,
        activeRate: 0,
        tryOnsGrowth: 0,
        revenueGrowth: 0,
        avgSessionsPerUser: 0
      });

    } catch (err) {
      console.error('Error fetching clients:', err);
      setError(err.message);
      setClients([]);
      setStats({
        newClientsThisMonth: 0,
        activeRate: 0,
        tryOnsGrowth: 0,
        revenueGrowth: 0,
        avgSessionsPerUser: 0
      });
    } finally {
      setLoading(false);
    }
  };

  // Helper function to format last active time
  const formatLastActive = (date) => {
    if (!date) return 'Never';
    const now = new Date();
    const lastActive = new Date(date);
    const diffInHours = Math.floor((now - lastActive) / (1000 * 60 * 60));

    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours} hours ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays} days ago`;
    const diffInWeeks = Math.floor(diffInDays / 7);
    return `${diffInWeeks} weeks ago`;
  };

  const handleFormChange = (e) => {
    const { name, value } = e.target;
    setClientForm(prev => ({ ...prev, [name]: value }));
  };

  const handleSuggestPassword = () => {
    setClientForm(prev => ({ ...prev, password: generatePassword() }));
  };

  const resetForm = () => {
    setClientForm({
      companyName: '',
      contactName: '',
      website: '',
      email: '',
      password: '',
      phone: '',
      industry: '',
      productType: 'watches',
      subscriptionPlan: 'basic'
    });
    setEditingClient(null);
  };

  const handleAddClient = async (e) => {
    e.preventDefault();
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';
      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;

      const response = await fetch(`${apiUrl}/api/clients`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(clientForm)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create client');
      }

      await fetchClients();
      setShowModal(false);
      resetForm();
    } catch (err) {
      console.error('Error creating client:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleEditClient = async (e) => {
    e.preventDefault();
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';
      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;

      const response = await fetch(`${apiUrl}/api/clients/${editingClient._id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(clientForm)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update client');
      }

      await fetchClients();
      setShowModal(false);
      resetForm();
    } catch (err) {
      console.error('Error updating client:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteClient = async (clientId) => {
    if (!window.confirm('Are you sure you want to delete this client?')) {
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';
      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;

      const response = await fetch(`${apiUrl}/api/clients/${clientId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to delete client');
      }

      await fetchClients();
    } catch (err) {
      console.error('Error deleting client:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const openEditModal = (client) => {
    setEditingClient(client);
    setClientForm({
      companyName: client.companyName || '',
      contactName: client.contactName || '',
      website: client.website || '',
      email: client.email || '',
      password: '', // Don't pre-fill password
      phone: client.phone || '',
      industry: client.industry || '',
      productType: client.productType || 'watches',
      subscriptionPlan: client.subscriptionPlan || 'basic'
    });
    setShowModal(true);
  };

  const openAddModal = () => {
    resetForm();
    setShowModal(true);
  };

  const openDetailsModal = (client) => {
    setSelectedClient(client);
    setShowDetailsModal(true);
  };

  const openCodeModal = (client) => {
    setSelectedClient(client);
    setShowCodeModal(true);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <AdminSidebar isOpen={isSidebarOpen} onClose={() => setIsSidebarOpen(false)} collapsed={collapsed} setCollapsed={setCollapsed} />
      <AdminNavbar toggleSidebar={toggleSidebar} collapsed={collapsed} />

      {/* Main Content */}
      <main className={`${mainMargin} pt-16 transition-all duration-300`}>
        <div className="p-4 md:p-6">
          {/* Page Header */}
          <div className="mb-6 flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Client Management</h1>
              <p className="text-gray-600">Manage your virtual try-on clients and track their performance.</p>
            </div>
            <button
              className="inline-flex items-center px-4 py-2 bg-[#2D8C88] text-white rounded-lg shadow-sm hover:bg-[#236b68] focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2"
              onClick={openAddModal}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Client
            </button>
          </div>

          {/* Stats Overview */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white rounded-xl shadow-sm p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Clients</p>
                  <p className="text-2xl font-semibold text-gray-900 mt-1">{loading ? '...' : clients.length}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center">
                  <Users className="h-6 w-6 text-blue-500" />
                </div>
              </div>
              <div className="mt-4">
                <span className="text-sm font-medium text-green-600">+{stats.newClientsThisMonth} new</span>
                <span className="text-sm text-gray-600 ml-2">this month</span>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="bg-white rounded-xl shadow-sm p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Clients</p>
                  <p className="text-2xl font-semibold text-gray-900 mt-1">{loading ? '...' : clients.filter(c => c.subscriptionStatus === 'active').length}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-green-500/10 flex items-center justify-center">
                  <TrendingUp className="h-6 w-6 text-green-500" />
                </div>
              </div>
              <div className="mt-4">
                <span className="text-sm font-medium text-green-600">{stats.activeRate.toFixed(1)}%</span>
                <span className="text-sm text-gray-600 ml-2">active rate</span>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-white rounded-xl shadow-sm p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Try-Ons</p>
                  <p className="text-2xl font-semibold text-gray-900 mt-1">{loading ? '...' : clients.reduce((sum, c) => sum + (c.analytics?.totalSessions || 0), 0).toLocaleString()}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center">
                  <Eye className="h-6 w-6 text-[#2D8C88]" />
                </div>
              </div>
              <div className="mt-4">
                <span className={`text-sm font-medium ${stats.tryOnsGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {stats.tryOnsGrowth >= 0 ? '+' : ''}{stats.tryOnsGrowth.toFixed(1)}%
                </span>
                <span className="text-sm text-gray-600 ml-2">this month</span>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="bg-white rounded-xl shadow-sm p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Unique Users</p>
                  <p className="text-2xl font-semibold text-gray-900 mt-1">{loading ? '...' : clients.reduce((sum, c) => sum + (c.analytics?.uniqueUsers?.length || 0), 0).toLocaleString()}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-purple-500/10 flex items-center justify-center">
                  <Users className="h-6 w-6 text-purple-500" />
                </div>
              </div>
              <div className="mt-4">
                <span className="text-sm font-medium text-gray-600">
                  Avg {stats.avgSessionsPerUser?.toFixed(1) || '0'} sessions/user
                </span>
              </div>
            </motion.div>
          </div>

          {/* Add/Edit Client Modal */}
          {showModal && (
            <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
              <div className="bg-white rounded-xl shadow-lg w-full max-w-2xl p-6 relative">
                <button
                  className="absolute top-3 right-3 text-gray-400 hover:text-gray-600"
                  onClick={() => setShowModal(false)}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
                <h2 className="text-2xl font-bold text-gray-900 mb-6">{editingClient ? 'Edit Client' : 'Add New Client'}</h2>
                <form onSubmit={editingClient ? handleEditClient : handleAddClient} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Company Name</label>
                      <input
                        type="text"
                        name="companyName"
                        value={clientForm.companyName}
                        onChange={handleFormChange}
                        required
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Contact Name</label>
                      <input
                        type="text"
                        name="contactName"
                        value={clientForm.contactName}
                        onChange={handleFormChange}
                        required
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                      <input
                        type="email"
                        name="email"
                        value={clientForm.email}
                        onChange={handleFormChange}
                        required
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Website</label>
                      <input
                        type="url"
                        name="website"
                        value={clientForm.website}
                        onChange={handleFormChange}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                      <input
                        type="tel"
                        name="phone"
                        value={clientForm.phone}
                        onChange={handleFormChange}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Industry</label>
                      <input
                        type="text"
                        name="industry"
                        value={clientForm.industry}
                        onChange={handleFormChange}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Product Type</label>
                      <select
                        name="productType"
                        value={clientForm.productType}
                        onChange={handleFormChange}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent"
                      >
                        <option value="watches">Watches</option>
                        <option value="bracelets">Bracelets</option>
                        <option value="both">Both</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Subscription Plan</label>
                      <select
                        name="subscriptionPlan"
                        value={clientForm.subscriptionPlan}
                        onChange={handleFormChange}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent"
                      >
                        <option value="basic">Basic</option>
                        <option value="premium">Premium</option>
                        <option value="enterprise">Enterprise</option>
                      </select>
                    </div>
                  </div>
                  {!editingClient && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Password</label>
                      <div className="flex gap-2">
                        <input
                          type="text"
                          name="password"
                          value={clientForm.password}
                          onChange={handleFormChange}
                          required
                          className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent"
                        />
                        <button
                          type="button"
                          onClick={handleSuggestPassword}
                          className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2"
                        >
                          Suggest
                        </button>
                      </div>
                    </div>
                  )}
                  <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                    <button
                      type="button"
                      onClick={() => setShowModal(false)}
                      className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="px-4 py-2 bg-[#2D8C88] text-white rounded-lg hover:bg-[#236b68] focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2"
                    >
                      {editingClient ? 'Update Client' : 'Add Client'}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          )}

          {/* Filters */}
          <div className="bg-white rounded-xl shadow-sm p-4 mb-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <input
                  type="text"
                  placeholder="Search clients..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent"
                />
              </div>
              <div className="w-full md:w-48">
                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent"
                >
                  <option value="all">All Status</option>
                  <option value="active">Active</option>
                  <option value="pending">Pending</option>
                </select>
              </div>
            </div>
          </div>

          {/* Clients Table */}
          <div className="bg-white rounded-xl shadow-sm overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell">Try-Ons</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell">Conversion</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell">Status</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell">Integration</th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {loading ? (
                    <tr>
                      <td colSpan="6" className="px-4 py-8 text-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#2D8C88] mx-auto"></div>
                      </td>
                    </tr>
                  ) : error ? (
                    <tr>
                      <td colSpan="6" className="px-4 py-8 text-center text-red-600">
                        Error loading clients: {error}
                      </td>
                    </tr>
                  ) : clients.length === 0 ? (
                    <tr>
                      <td colSpan="6" className="px-4 py-8 text-center text-gray-500">
                        No clients found
                      </td>
                    </tr>
                  ) : (
                    clients.map((client) => (
                      <motion.tr
                        key={client._id}
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        className="hover:bg-gray-50"
                      >
                        <td className="px-4 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 h-10 w-10">
                              <div className="h-10 w-10 rounded-full bg-[#2D8C88] flex items-center justify-center text-white">
                                {client.companyName?.charAt(0) || 'C'}
                              </div>
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">{client.companyName}</div>
                              <div className="text-sm text-gray-500">{client.email}</div>
                              <div className="text-sm text-gray-500 lg:hidden">
                                {client.analytics?.totalSessions?.toLocaleString() || '0'} try-ons • {client.analytics?.conversionRate || '0'}% conversion
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap hidden lg:table-cell">
                          <div className="text-sm font-medium text-gray-900">{client.analytics?.totalSessions?.toLocaleString() || '0'}</div>
                          <div className="text-sm text-gray-500">{client.analytics?.productCount || '0'} products</div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap hidden lg:table-cell">
                          <div className="text-sm font-medium text-gray-900">{client.analytics?.conversionRate || '0'}%</div>
                          <div className="text-sm text-gray-500">${client.analytics?.revenue?.toLocaleString() || '0'} revenue</div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap hidden md:table-cell">
                          <div className="flex flex-col space-y-1">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              client.subscriptionStatus === 'active' ? 'bg-green-100 text-green-800' :
                              client.subscriptionStatus === 'trial' ? 'bg-blue-100 text-blue-800' :
                              'bg-yellow-100 text-yellow-800'
                            }`}>
                              {client.subscriptionStatus}
                            </span>
                            <span className="text-xs text-gray-500">{formatLastActive(client.analytics?.lastActive)}</span>
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap hidden lg:table-cell">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            client.subscriptionPlan === 'enterprise' ? 'bg-purple-100 text-purple-800' :
                            client.subscriptionPlan === 'premium' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'
                          }`}>
                            {client.subscriptionPlan}
                          </span>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div className="flex justify-end space-x-2">
                            <button
                              className="text-[#2D8C88] hover:text-[#2D8C88]/80 p-1"
                              title="View Details"
                              onClick={() => openDetailsModal(client)}
                            >
                              <Eye className="h-4 w-4" />
                            </button>
                            <button
                              className="text-blue-600 hover:text-blue-800 p-1"
                              title="Integration Code"
                              onClick={() => openCodeModal(client)}
                            >
                              <Code className="h-4 w-4" />
                            </button>
                            <button
                              className="text-gray-600 hover:text-gray-800 p-1"
                              onClick={() => openEditModal(client)}
                              title="Edit Client"
                            >
                              <Edit className="h-4 w-4" />
                            </button>
                            <button
                              className="text-red-600 hover:text-red-800 p-1"
                              onClick={() => handleDeleteClient(client._id)}
                              title="Delete Client"
                            >
                              <Trash2 className="h-4 w-4" />
                            </button>
                          </div>
                        </td>
                      </motion.tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </main>

      {/* Client Details Modal */}
      {showDetailsModal && selectedClient && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
          <div className="bg-white rounded-xl shadow-lg w-full max-w-2xl p-6 relative">
            <button
              className="absolute top-3 right-3 text-gray-400 hover:text-gray-600"
              onClick={() => setShowDetailsModal(false)}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
            <div className="flex items-center space-x-4 mb-6">
              <div className="h-16 w-16 rounded-full bg-[#2D8C88] flex items-center justify-center text-white text-2xl">
                {selectedClient.companyName?.charAt(0) || 'C'}
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900">{selectedClient.companyName}</h2>
                <p className="text-gray-600">{selectedClient.email}</p>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Company Information</h3>
                <div className="space-y-3">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Contact Name</p>
                    <p className="text-gray-900">{selectedClient.contactName}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Website</p>
                    <p className="text-gray-900">{selectedClient.website || 'N/A'}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Phone</p>
                    <p className="text-gray-900">{selectedClient.phone || 'N/A'}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Industry</p>
                    <p className="text-gray-900">{selectedClient.industry || 'N/A'}</p>
                  </div>
                </div>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Analytics</h3>
                <div className="space-y-3">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Total Try-Ons</p>
                    <p className="text-gray-900">{selectedClient.analytics?.totalSessions?.toLocaleString() || '0'}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Unique Users</p>
                    <p className="text-gray-900">{selectedClient.analytics?.uniqueUsers?.length?.toLocaleString() || '0'}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Conversion Rate</p>
                    <p className="text-gray-900">{selectedClient.analytics?.conversionRate || '0'}%</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Last Active</p>
                    <p className="text-gray-900">{formatLastActive(selectedClient.analytics?.lastActive)}</p>
                  </div>
                </div>
              </div>
            </div>
            <div className="mt-6 pt-6 border-t border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Subscription Details</h3>
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <p className="text-sm font-medium text-gray-500">Plan</p>
                  <p className="text-gray-900 capitalize">{selectedClient.subscriptionPlan}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Status</p>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    selectedClient.subscriptionStatus === 'active' ? 'bg-green-100 text-green-800' :
                    selectedClient.subscriptionStatus === 'trial' ? 'bg-blue-100 text-blue-800' :
                    'bg-yellow-100 text-yellow-800'
                  }`}>
                    {selectedClient.subscriptionStatus}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Integration Code Modal */}
      {showCodeModal && selectedClient && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
          <div className="bg-white rounded-xl shadow-lg w-full max-w-2xl p-6 relative">
            <button
              className="absolute top-3 right-3 text-gray-400 hover:text-gray-600"
              onClick={() => setShowCodeModal(false)}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Integration Code for {selectedClient.companyName}</h2>
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">HTML Integration</h3>
                <div className="bg-gray-50 rounded-lg p-4">
                  <pre className="text-sm text-gray-800 overflow-x-auto">
                    {`<script src="https://tryon.viatryon.com/sdk.js" data-client-id="${selectedClient._id}"></script>`}
                  </pre>
                </div>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">React Component</h3>
                <div className="bg-gray-50 rounded-lg p-4">
                  <pre className="text-sm text-gray-800 overflow-x-auto">
                    {`import { VirtualTryOn } from '@viatryon/react';

function App() {
  return (
    <VirtualTryOn
      clientId="${selectedClient._id}"
      productId="your-product-id"
    />
  );
}`}
                  </pre>
                </div>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">API Key</h3>
                <div className="bg-gray-50 rounded-lg p-4">
                  <p className="text-sm text-gray-800 break-all">{selectedClient.apiKey || 'No API key generated'}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Clients; 